# PLC Data Exploration Documentation

## Overview
The PLC (Programmable Logic Controller) Data Exploration module provides a comprehensive interface for analyzing PLC time series data with advanced visualization capabilities. This module is designed to be completely separate from batch exploration while maintaining similar UI patterns and functionality.

## Architecture

### Component Structure
```
PLCExploration/
├── PLCContent.tsx              # Main container component
├── PLCGridLayout.tsx           # Grid layout management
├── PLCGridItem.tsx             # Individual panel wrapper
├── PLCSidebar.tsx              # Panel selection sidebar
├── PLCPanelConfigDrawer.tsx    # Configuration drawer
├── panels/
│   └── PLCTimeSeriesPanel.tsx  # Time series visualization panel
├── services/                   # API service handlers
├── styles/
│   └── PLCStyles.css          # Component-specific styles
└── types/
    └── PLCTypes.ts            # TypeScript definitions
```

### Key Features

#### 1. Drag & Drop Panel System
- **Single Panel Restriction**: Only one time series panel can be active at a time
- **Grid Layout**: Uses react-grid-layout for responsive panel positioning
- **Panel State Management**: Maintains panel configurations and layout state
- **Auto-sizing**: Panels automatically resize based on content and screen mode

#### 2. Time Series Panel Configuration

##### Data Range Section
- **Date/Time Selection**: Ant Design RangePicker for start/end datetime selection
- **Recent Searches**: Stores and displays last 5 date range searches in localStorage
- **Validation**: Requires both start and end dates before data fetching
- **Submit Button**: Positioned above other sections for immediate data loading

##### Basic Configuration
- **X-Axis Selection**: Dropdown with DateTime (default) and all available columns
- **Y-Axis Multi-Select**: Multiple column selection with immediate graph updates
- **Grouping Options**: 
  - None (default)
  - Group by any available column (BatchId, RecipeNo, etc.)
  - Advanced grouping creates separate lines for each distinct value

##### Advanced Configuration
- **Window Mode**: Toggle for batch comparison analysis
- **Target Selection**: Checkboxes for grouping values when window mode enabled
- **Pre/Post Dropdowns**: Numeric inputs for batch window analysis
- **Dynamic Updates**: Configuration changes trigger immediate API calls

#### 3. API Integration

##### Two-Step API Flow
1. **get-batch-meta**: Fetches available batches with quality information
   - Endpoint: `AIML_API/get-batch-meta`
   - Payload: `{start_datetime, end_datetime}`
   - Returns: Available batches with quality scores and compare_limit

2. **batch-comparison**: Handles data fetching and visualization
   - Endpoint: `AIML_API/batch-comparison`
   - Payload: `{batch_ids[], x_axis, y_axis[], identifier, compare_limit}`
   - Returns: Complete ECharts configuration with columnOptions structure

##### Data Structure
```typescript
// API Response Format
{
  columnOptions: {
    [columnName]: {
      series: [{
        name: string,
        data: [datetime, value][],
        color: string,
        lineStyle: { width: number }
      }]
    }
  },
  qualityBatchIds: [{
    batch_id: string,
    batch_quality: number,
    is_batch_good: boolean
  }]
}
```

#### 4. Visualization Features

##### Chart Display
- **ECharts Integration**: Uses echarts-for-react for all visualizations
- **Stacked View**: Multiple columns displayed as separate stacked charts
- **No Animations**: Charts configured with `animation: false` for performance
- **Auto-resize**: Charts fill complete panel area in both normal and full-screen modes
- **Small Data Points**: 4px circle symbols for clean visualization

##### Chart Formatting
- **Decimal Precision**: All values formatted to 2 decimal places (except DateTime)
- **Axis Labels**: Clear X and Y axis labels with column names
- **No Decorative Elements**: Clean, minimal chart design
- **Responsive Grid**: Charts adjust layout based on number of selected columns

##### Interactive Features
- **Enhanced Tooltips**: Rich tooltips showing batch metadata, quality scores, and time ranges
- **Brush Selection**: X-axis only brush selection for data filtering
- **Data Zoom**: Both inside (mouse wheel) and slider zoom controls
- **Legend Management**: Scrollable legends with series information

#### 5. State Management

##### Panel State Persistence
- **Configuration Storage**: Panel configurations stored in component state
- **Column Selection**: Maintains selected Y-axis columns across configuration changes
- **Layout Persistence**: Grid layout maintained when switching between modes

##### Redux Integration
- **Systems State**: Accesses Redux systems state for dynamic dropdown population
- **Target Variables**: Extracts distinct field values from configurations.rules array
- **Default Selection**: Uses first distinct value as default target variable

#### 6. User Experience Features

##### Loading States
- **Master Loading**: Unified loading state for all critical operations
- **Operation-Specific Messages**: Different loading messages for different operations
- **Drawer Persistence**: Configuration drawer remains open during data fetching
- **Submit Button State**: Buttons remain enabled for multiple submissions

##### Error Handling
- **API Error Display**: Clear error messages for failed API calls
- **Validation Messages**: Form validation with user-friendly error messages
- **Empty States**: Informative empty states with configuration prompts
- **Fallback Handling**: Graceful degradation when data is unavailable

##### Configuration Management
- **Pre-selection**: Previously selected columns shown in dropdowns when reopening
- **Immediate Updates**: Y-axis dropdown changes trigger immediate graph updates
- **Default Values**: Sensible defaults for all configuration options
- **Validation**: Date/time validation before API submission

## Technical Implementation

### Performance Optimizations
- **Backend Data Processing**: Complete ECharts configurations returned from backend
- **Minimal Frontend Processing**: No data transformation on frontend
- **Efficient Re-rendering**: Chart disposal and recreation for configuration changes
- **Optimized API Calls**: Single panel refresh when configuration changes

### Code Quality
- **TypeScript**: Comprehensive type definitions in PLCTypes.ts
- **Clean Architecture**: Separation of concerns between components
- **Reusable Components**: Modular design for future panel types
- **Consistent Patterns**: Follows established batch exploration patterns

### Future Extensibility
- **Panel Type System**: Enum-based system for adding new panel types
- **Configuration Schema**: Flexible configuration structure for new features
- **API Abstraction**: Service layer ready for additional endpoints
- **Component Modularity**: Easy addition of new visualization types

## Usage Guidelines

### Basic Workflow
1. **Panel Addition**: Drag time series panel from sidebar to grid
2. **Date Configuration**: Select start/end datetime range and submit
3. **Column Selection**: Choose X-axis and Y-axis columns from dropdowns
4. **Grouping Options**: Optionally select grouping column for multi-series display
5. **Advanced Features**: Enable window mode for batch comparison analysis

### Best Practices
- **Date Range Selection**: Use reasonable date ranges to avoid performance issues
- **Column Limitation**: Select appropriate number of Y-axis columns for readability
- **Grouping Strategy**: Use grouping for comparative analysis across batches
- **Full-Screen Mode**: Utilize full-screen for detailed analysis of complex data

### Configuration Tips
- **Recent Searches**: Leverage recent date searches for quick access to common ranges
- **Default Settings**: X-axis defaults to 'Absolute' mode with DateTime column
- **Stacked Display**: Multiple Y-axis selections automatically show stacked graphs
- **Quality Indicators**: Pay attention to batch quality scores in tooltips and legends

## API Endpoints and Data Flow

### 1. Batch Metadata Endpoint
```
POST AIML_API/get-batch-meta
Payload: {
  start_datetime: "YYYY-MM-DD HH:MM",
  end_datetime: "YYYY-MM-DD HH:MM"
}
Response: {
  available_batches: string[],
  quality_batch_ids: [{
    batch_id: string,
    batch_quality: number,
    is_batch_good: boolean
  }],
  compare_limit: number
}
```

### 2. Batch Comparison Endpoint
```
POST AIML_API/batch-comparison
Payload: {
  batch_ids: string[],
  x_axis: string,
  y_axis: string[],
  identifier: string,
  compare_limit: number
}
Response: {
  columnOptions: {
    [columnName]: {
      series: [{
        name: string,
        data: [datetime, value][],
        color: string,
        lineStyle: { width: number, color: string },
        symbolSize: number
      }]
    }
  }
}
```

## Component Interactions

### PLCContent → PLCGridLayout → PLCGridItem → PLCTimeSeriesPanel
1. **PLCContent**: Manages overall state, panel configurations, and drawer interactions
2. **PLCGridLayout**: Handles drag-drop functionality and grid layout management
3. **PLCGridItem**: Wraps individual panels with common functionality (remove, fullscreen, settings)
4. **PLCTimeSeriesPanel**: Renders charts and handles panel-specific logic

### Configuration Flow
1. User opens configuration drawer via settings button
2. PLCPanelConfigDrawer loads current configuration and available options
3. Date range submission triggers get-batch-meta API call
4. Batch metadata populates available batches and quality information
5. Column selection triggers batch-comparison API call
6. Chart data processed and displayed in PLCTimeSeriesPanel

## State Management Details

### Component State Structure
```typescript
// PLCContent State
{
  items: PLCGridItemData[],
  layout: PLCLayout,
  nextId: number,
  drawerOpen: boolean,
  selectedPanelId: string | null,
  panelConfigurations: Record<string, PLCPanelConfiguration>,
  currentlyDisplayedColumns: string[]
}

// PLCPanelConfiguration Structure
{
  dataRange: {
    startDate: string | null,
    endDate: string | null,
    startTime: string | null,
    endTime: string | null
  },
  basic: {
    xAxisColumn: string,
    selectedColumns: { indices: number[], headers: string[] },
    group: string | null
  },
  advanced: {
    windowMode: boolean,
    windowConfig?: {
      enabled: boolean,
      groupColumn: string,
      target_batch_ids?: string[],
      pre_batch_ids?: string[],
      post_batch_ids?: string[]
    }
  },
  apiData: {
    columnOptions: Record<string, any>,
    columns: string[],
    selectedBatches: string[],
    availableBatches: string[],
    qualityBatchIds: Array<{
      batch_id: string,
      batch_quality: number,
      is_batch_good: boolean
    }>,
    compareLimit: number,
    metadata: {
      columnNames: string[],
      hasData: boolean,
      totalColumns: number,
      processedColumns: number,
      dateColumn?: string,
      error?: string
    },
    chartData: any,
    groupedData: Record<string, any>
  }
}
```

## Error Handling and Edge Cases

### API Error Scenarios
- **Network Failures**: Display connection error with retry option
- **Invalid Date Range**: Show validation message for invalid date selections
- **No Data Available**: Display empty state with reconfiguration option
- **Batch Limit Exceeded**: Warn user about compare_limit restrictions
- **Column Not Found**: Handle missing columns gracefully with fallback options

### UI Edge Cases
- **Empty Column Selection**: Show "No Y-axis Columns Selected" message
- **Large Dataset Rendering**: Implement loading states and progressive rendering
- **Responsive Layout**: Handle different screen sizes and panel arrangements
- **Configuration Persistence**: Maintain settings across browser sessions

### Performance Considerations
- **Chart Disposal**: Properly dispose ECharts instances to prevent memory leaks
- **API Debouncing**: Prevent rapid API calls during configuration changes
- **Data Caching**: Cache API responses for repeated requests
- **Lazy Loading**: Load panel components only when needed

## Development Guidelines

### Adding New Panel Types
1. Define new enum value in PLCComponentType
2. Create panel component in panels/ directory
3. Add panel configuration interface to PLCTypes.ts
4. Update PLCGridItem to handle new panel type
5. Add panel to PLCSidebar for drag-drop functionality

### Extending Configuration Options
1. Update PLCPanelConfiguration interface
2. Add form fields to PLCPanelConfigDrawer
3. Handle new configuration in panel components
4. Update API payload structure if needed
5. Add validation and error handling

### Testing Considerations
- **Unit Tests**: Test individual component functionality
- **Integration Tests**: Test API integration and data flow
- **E2E Tests**: Test complete user workflows
- **Performance Tests**: Monitor chart rendering performance
- **Accessibility Tests**: Ensure keyboard navigation and screen reader support

## Component Implementation Details

### PLCSidebar Component
- **Navigation**: Back button to return to main data exploration
- **Panel Selection**: Drag-and-drop interface for adding panels
- **Single Panel Restriction**: Time Series panel can only be added once
- **Visual Feedback**: Shows which panels are already active
- **Future Extensibility**: Ready for additional panel types

### PLCGridLayout Component
- **React Grid Layout**: Uses react-grid-layout for responsive grid system
- **Drag Indicators**: Visual feedback during panel drag operations
- **Drop Zones**: Intelligent drop zone calculation and validation
- **Layout Persistence**: Maintains panel positions and sizes
- **Responsive Design**: Adapts to different screen sizes

### PLCGridItem Component
- **Panel Wrapper**: Common wrapper for all panel types
- **Action Buttons**: Remove, fullscreen, and settings buttons
- **Fullscreen Mode**: Toggle between normal and fullscreen display
- **Configuration Access**: Direct access to panel configuration drawer
- **Error Boundaries**: Handles panel-specific errors gracefully

## Data Processing Pipeline

### 1. Date Range Selection
```
User selects date range → Validation → get-batch-meta API call →
Available batches populated → Quality information displayed
```

### 2. Column Configuration
```
User selects columns → Form validation → batch-comparison API call →
Chart data processed → ECharts configuration generated → Chart rendered
```

### 3. Grouping and Filtering
```
User selects grouping → Data regrouped by selected column →
Multiple series created → Chart updated with new series
```

### 4. Window Mode Analysis
```
Window mode enabled → Target batches selected → Pre/Post configuration →
Batch comparison analysis → Enhanced visualization with quality indicators
```

## Memory Management and Performance

### Chart Instance Management
- **Proper Disposal**: ECharts instances disposed before re-creation
- **Memory Leak Prevention**: Event listeners and observers cleaned up
- **Efficient Re-rendering**: Chart updates triggered only when necessary
- **Canvas Optimization**: Uses canvas renderer for better performance

### API Call Optimization
- **Request Debouncing**: Prevents rapid successive API calls
- **Response Caching**: Caches API responses for repeated requests
- **Batch Processing**: Combines multiple configuration changes into single API call
- **Error Recovery**: Implements retry logic for failed requests

### State Management Efficiency
- **Minimal Re-renders**: Uses React.memo and useMemo for optimization
- **State Normalization**: Efficient state structure for large datasets
- **Selective Updates**: Updates only changed portions of state
- **Memory Cleanup**: Clears unused data when panels are removed

## Security and Validation

### Input Validation
- **Date Range Validation**: Ensures valid date ranges and formats
- **Column Selection Validation**: Validates selected columns exist in dataset
- **Batch ID Validation**: Verifies batch IDs are valid and accessible
- **API Payload Sanitization**: Sanitizes all API request payloads

### Error Handling
- **API Error Recovery**: Graceful handling of API failures
- **Data Validation**: Validates API response structure and content
- **User Feedback**: Clear error messages for all failure scenarios
- **Fallback Mechanisms**: Provides fallback options when primary features fail

## Accessibility Features

### Keyboard Navigation
- **Tab Order**: Logical tab order through all interactive elements
- **Keyboard Shortcuts**: Support for common keyboard shortcuts
- **Focus Management**: Proper focus management in modals and drawers
- **Screen Reader Support**: ARIA labels and descriptions for all elements

### Visual Accessibility
- **Color Contrast**: High contrast colors for better visibility
- **Font Sizing**: Scalable fonts that respect user preferences
- **Visual Indicators**: Clear visual feedback for all interactions
- **Responsive Design**: Works across different screen sizes and orientations

## Deployment and Configuration

### Environment Variables
```
REACT_APP_AIML_API_URL=<backend_api_url>
REACT_APP_PLC_CACHE_DURATION=<cache_duration_ms>
REACT_APP_PLC_MAX_BATCHES=<max_batch_limit>
```

### Build Configuration
- **Code Splitting**: Lazy loading for PLC components
- **Bundle Optimization**: Tree shaking for unused code elimination
- **Asset Optimization**: Compressed images and optimized CSS
- **Source Maps**: Development source maps for debugging

### Monitoring and Analytics
- **Performance Metrics**: Chart rendering time and API response times
- **Error Tracking**: Comprehensive error logging and reporting
- **User Analytics**: Usage patterns and feature adoption metrics
- **Health Checks**: API endpoint health monitoring

## Future Enhancements

### Planned Features
- **Real-time Data**: Live data streaming for real-time monitoring
- **Advanced Analytics**: Statistical analysis and trend detection
- **Export Capabilities**: Enhanced export options (Excel, CSV, PDF)
- **Collaborative Features**: Sharing and collaboration tools
- **Mobile Support**: Responsive design for mobile devices

### Technical Improvements
- **WebSocket Integration**: Real-time data updates
- **Advanced Caching**: Intelligent caching strategies
- **Offline Support**: Offline data access and synchronization
- **Performance Optimization**: Further performance enhancements
- **Accessibility Enhancements**: Additional accessibility features
