import React, { useState, useRef, useCallback } from 'react';
import { useSelector } from 'react-redux';
import {
  PLCContentProps,
  PLCGridItemData,
  PLCLayout,
  PLCComponentType,
  PLCPanelConfiguration,
  DEFAULT_PLC_PANEL_CONFIG,
  PLC_PANEL_SIZES
} from './types/PLCTypes';
import PLCGridLayout from './PLCGridLayout';
import PLCPanelConfigDrawer from './PLCPanelConfigDrawer';
import { postRequest } from '../../../../utils/apiHandler';
import { message } from 'antd';
import dayjs from 'dayjs';
import './styles/PLCStyles.css';

const PLCContent: React.FC<PLCContentProps> = ({
  selectedDataSource, // Will be used in future tasks
  onDataSourceChange, // Will be used in future tasks
  activePanels = [],
  onPanelsChange,
  // New props for date range and batch management
  onDateRangeSubmit,
  onBatchSelectionChange,
  availableBatches = [],
  qualityBatchIds = [],
  selectedBatches = [],
  compareLimit = 5,
  isLoadingBatches = false
}) => {
  // State management
  const [items, setItems] = useState<PLCGridItemData[]>([]);
  const [layout, setLayout] = useState<PLCLayout>([]);
  const [nextId, setNextId] = useState(1);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [selectedPanelId, setSelectedPanelId] = useState<string | null>(null);
  const [panelConfigurations, setPanelConfigurations] = useState<Record<string, PLCPanelConfiguration>>({});
  const [currentlyDisplayedColumns, setCurrentlyDisplayedColumns] = useState<string[]>([]);

  // Get selected systems from Redux state
  const selectSystems = useSelector((state: any) => state.systems.systems);

  // Helper function to extract system names from selected systems
  const getSystemNames = () => {
    if (!selectSystems || !selectSystems.length || !selectSystems[0]?.systems) {
      return [];
    }

    return selectSystems[0].systems.map((system: any) => ({
      systemId: system.systemId,
      systemName: system.systemName
    }));
  };

  // Refs
  const gridLayoutRef = useRef<{getLayout: () => PLCLayout, getItems: () => PLCGridItemData[]}>(null);

  // Handle dropping a panel from sidebar
  const handleDrop = useCallback(async (componentType: PLCComponentType, x: number, y: number) => {
    // Check if this panel type already exists (single panel restriction)
    const existingPanel = items.find(item => item.type === componentType);
    if (existingPanel) {
      return;
    }

    // Check if date range and batches are configured
    const isDateRangeConfigured = availableBatches.length > 0;
    const isBatchesSelected = selectedBatches.length > 0;
    const isFullyConfigured = isDateRangeConfigured && isBatchesSelected;

    let initialConfig = DEFAULT_PLC_PANEL_CONFIG;
    let isConfigured = false;

    // If fully configured, automatically trigger API call and populate panel
    if (isFullyConfigured) {
      try {
        // Create configuration with current sidebar state
        const systems = getSystemNames();
        const systemNames = systems.map((s: any) => s.systemName).join(' + ') || '';

        // Get current date range from sidebar (we'll need to pass this from parent)
        // For now, use default range - this will be improved when we integrate with sidebar state
        const startDateTime = dayjs().subtract(7, 'days').startOf('day');
        const endDateTime = dayjs().endOf('day');

        const apiPayload = {
          start_datetime: startDateTime.format('YYYY-MM-DD HH:mm:ss'),
          end_datetime: endDateTime.format('YYYY-MM-DD HH:mm:ss'),
          batch_ids: selectedBatches,
          x_axis: 'DateTime',
          y_axis: [], // Will be populated from API response
          identifier: 'BatchId',
          compare_limit: compareLimit
        };

        const response = await postRequest('/file/explore-plc/batch-comparison', apiPayload);

        if (response.data.data) {
          const apiData = response.data.data;
          const availableColumns = apiData.columns || [];
          const firstColumn = availableColumns.find((col: string) => col !== 'DateTime');

          initialConfig = {
            dataRange: {
              startDate: startDateTime.format('YYYY-MM-DD'),
              endDate: endDateTime.format('YYYY-MM-DD'),
              startTime: startDateTime.format('HH:mm'),
              endTime: endDateTime.format('HH:mm'),
            },
            basic: {
              xAxisColumn: 'DateTime',
              selectedColumns: firstColumn ? { indices: [0], headers: [firstColumn] } : { indices: [], headers: [] },
              group: 'BatchId',
            },
            advanced: {
              windowMode: false,
            },
            title: firstColumn ? `Batch Comparison - ${firstColumn}` : 'Batch Comparison - No Data Selected',
            panelType: 'PLCBatchComparisonPanel' as any,
            lastModified: new Date().toISOString(),
            apiData: {
              ...apiData,
              selectedBatches: selectedBatches,
              availableBatches: availableBatches,
              qualityBatchIds: qualityBatchIds,
              compareLimit: compareLimit,
              columns: availableColumns
            }
          };
          isConfigured = true;
          message.success('Panel configured and data loaded automatically');
        }
      } catch (error) {
        console.error('Error auto-configuring panel:', error);
        message.error('Failed to auto-configure panel');
      }
    }

    const newItem: PLCGridItemData = {
      id: `plc-panel-${nextId}`,
      type: componentType,
      title: componentType === PLCComponentType.PLCBatchComparisonPanel ? 'Batch Comparison' : 'Unknown Panel',
      config: initialConfig,
      isConfigured: isConfigured,
    };

    const newLayoutItem = {
      i: newItem.id,
      x: Math.max(0, Math.min(x, 12 - PLC_PANEL_SIZES.DEFAULT.w)),
      y: Math.max(0, y),
      w: PLC_PANEL_SIZES.DEFAULT.w,
      h: PLC_PANEL_SIZES.DEFAULT.h,
      minW: PLC_PANEL_SIZES.MIN.w,
      minH: PLC_PANEL_SIZES.MIN.h,
    };

    const updatedItems = [...items, newItem];
    setItems(updatedItems);
    setLayout(prev => [...prev, newLayoutItem]);
    setNextId(prev => prev + 1);

    // Store the configuration
    if (isConfigured) {
      setPanelConfigurations(prev => ({
        ...prev,
        [newItem.id]: initialConfig
      }));
    }

    // Notify parent about panel changes
    if (onPanelsChange) {
      const newActivePanels = updatedItems.map(item => item.type);
      onPanelsChange(newActivePanels);
    }
  }, [items, nextId, onPanelsChange, availableBatches, selectedBatches, qualityBatchIds, compareLimit, getSystemNames]);

  // Handle layout changes
  const handleLayoutChange = useCallback((newLayout: PLCLayout) => {
    setLayout(newLayout);
  }, []);

  // Handle items changes
  const handleItemsChange = useCallback((newItems: PLCGridItemData[]) => {
    setItems(newItems);
  }, []);

  // Handle panel removal
  const handlePanelRemove = useCallback((panelId: string) => {
    // Remove from configurations
    setPanelConfigurations(prev => {
      const newConfigs = { ...prev };
      delete newConfigs[panelId];
      return newConfigs;
    });

    // Notify parent about panel changes
    if (onPanelsChange) {
      const updatedItems = items.filter(item => item.id !== panelId);
      const newActivePanels = updatedItems.map(item => item.type);
      onPanelsChange(newActivePanels);
    }

  }, [items, onPanelsChange]);

  // Handle opening configuration drawer
  const handleOpenConfiguration = useCallback((panelId: string, displayedColumns?: string[]) => {
    setSelectedPanelId(panelId);
    setCurrentlyDisplayedColumns(displayedColumns || []);
    setDrawerOpen(true);
  }, []);

  // Handle configuration save
  const handleConfigurationSave = useCallback((panelId: string, config: PLCPanelConfiguration) => {
    // Update panel configurations
    setPanelConfigurations(prev => ({
      ...prev,
      [panelId]: config
    }));

    // Update the panel item to mark it as configured
    const updatedItems = items.map(item =>
      item.id === panelId
        ? { ...item, config, isConfigured: true, title: config.title || item.title }
        : item
    );
    setItems(updatedItems);


  }, [items]);

  // Handle drawer close
  const handleDrawerClose = useCallback(() => {
    setDrawerOpen(false);
    setSelectedPanelId(null);
  }, []);

  // Note: Date range and batch selection is now handled directly by WorkFlowContainer
  // PLCContent no longer needs to handle these operations

  // Get current panel configuration
  const getCurrentConfiguration = () => {
    if (!selectedPanelId) return undefined;
    return panelConfigurations[selectedPanelId] || DEFAULT_PLC_PANEL_CONFIG;
  };



  return (
    <>
      <div className="plc-content view-content flex-1 p-4 h-full relative">
        <PLCGridLayout
          ref={gridLayoutRef}
          items={items}
          layout={layout}
          onLayoutChange={handleLayoutChange}
          onItemsChange={handleItemsChange}
          onDrop={handleDrop}
          onPanelRemove={handlePanelRemove}
          onOpenConfiguration={handleOpenConfiguration}
        />
      </div>

      {/* Configuration Drawer */}
      <PLCPanelConfigDrawer
        open={drawerOpen}
        onClose={handleDrawerClose}
        panelId={selectedPanelId}
        configuration={getCurrentConfiguration()}
        availableFeatures={[]} // Will be populated with real features later
        onConfigurationSave={handleConfigurationSave}
      />
    </>
  );
};

export default PLCContent;
