import React from 'react';
import { Spin } from 'antd';

interface PLCFullScreenLoaderProps {
  loading: boolean;
  message?: string;
}

const PLCFullScreenLoader: React.FC<PLCFullScreenLoaderProps> = ({ 
  loading, 
  message = 'Loading...' 
}) => {
  if (!loading) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(255, 255, 255, 0.8)',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 9999,
      backdropFilter: 'blur(2px)'
    }}>
      <Spin size="large" />
      <div style={{
        marginTop: '16px',
        fontSize: '16px',
        fontWeight: 500,
        color: '#1890ff'
      }}>
        {message}
      </div>
    </div>
  );
};

export default PLCFullScreenLoader;
