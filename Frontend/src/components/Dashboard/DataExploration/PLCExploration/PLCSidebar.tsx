import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import {
  Button,
  Space,
  Collapse,
  Typography,
  DatePicker,
  Form,
  Select,
  message
} from 'antd';
import {
  ArrowLeftOutlined,
  PlusOutlined,
  MinusOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { PLCComponentType, PLCSidebarProps } from './types/PLCTypes';
import dayjs from 'dayjs';

const { Panel } = Collapse;
const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

const PLCSidebar: React.FC<PLCSidebarProps> = ({
  onBackClick,
  activePanels = [],
  availableDataSources = [],
  onDataSourceSelect,
  onDateRangeSubmit,
  onBatchSelectionChange,
  availableBatches = [],
  qualityBatchIds = [],
  selectedBatches = [],
  compareLimit = 5,
  isLoadingBatches = false
}) => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // Get selected systems from Redux state
  const selectSystems = useSelector((state: any) => state.systems.systems);

  // Recent searches management
  const [recentSearches, setRecentSearches] = useState<Array<{label: string, value: [any, any]}>>([]);
  const RECENT_SEARCHES_KEY = 'plc_recent_date_searches';
  const MAX_RECENT_SEARCHES = 5;

  // Helper function to extract system names from selected systems
  const getSystemNames = () => {
    if (!selectSystems || !selectSystems.length || !selectSystems[0]?.systems) {
      return [];
    }

    return selectSystems[0].systems.map((system: any) => ({
      systemId: system.systemId,
      systemName: system.systemName
    }));
  };

  // Load recent searches from localStorage
  useEffect(() => {
    const saved = localStorage.getItem(RECENT_SEARCHES_KEY);
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        const validSearches = parsed.map((search: any) => ({
          label: search.label,
          value: [dayjs(search.value[0]), dayjs(search.value[1])] as [any, any]
        }));
        setRecentSearches(validSearches);
      } catch (error) {
        console.error('Error loading recent searches:', error);
      }
    }

    // Set default date range (last 7 days)
    const defaultStartDateTime = dayjs().subtract(7, 'days').startOf('day');
    const defaultEndDateTime = dayjs().endOf('day');

    form.setFieldsValue({
      dateTimeRange: [defaultStartDateTime, defaultEndDateTime],
    });
  }, [form]);

  // Save a new date range to recent searches
  const saveToRecentSearches = (startDateTime: any, endDateTime: any) => {
    const newSearch = {
      label: `${startDateTime.format('MMM DD')} - ${endDateTime.format('MMM DD, YYYY')}`,
      value: [startDateTime, endDateTime] as [any, any]
    };

    const existing = recentSearches.filter(search =>
      search.label !== newSearch.label
    );

    const updated = [newSearch, ...existing].slice(0, MAX_RECENT_SEARCHES);
    setRecentSearches(updated);

    const toSave = updated.map(search => ({
      label: search.label,
      value: [search.value[0].toISOString(), search.value[1].toISOString()]
    }));

    localStorage.setItem(RECENT_SEARCHES_KEY, JSON.stringify(toSave));
  };

  // Create presets for RangePicker (common ranges + recent searches)
  const createDatePresets = () => {
    const commonPresets = [
      {
        label: 'Last 24 Hours',
        value: [dayjs().subtract(1, 'day'), dayjs()] as [any, any],
      },
      {
        label: 'Last 3 Days',
        value: [dayjs().subtract(3, 'days').startOf('day'), dayjs().endOf('day')] as [any, any],
      },
      {
        label: 'Last 7 Days',
        value: [dayjs().subtract(7, 'days').startOf('day'), dayjs().endOf('day')] as [any, any],
      },
      {
        label: 'Last 30 Days',
        value: [dayjs().subtract(30, 'days').startOf('day'), dayjs().endOf('day')] as [any, any],
      },
    ];

    const recentPresets = recentSearches.map(search => ({
      label: `📅 ${search.label}`,
      value: search.value,
    }));

    return [...commonPresets, ...recentPresets];
  };

  const handleBackClick = () => {
    if (onBackClick) {
      onBackClick();
      navigate('/?tab=data');
    } else {
      // Default behavior: navigate back to data tab
      navigate('/?tab=data');
    }
  };

  // Handle date range submission
  const handleDateRangeSubmit = async () => {
    try {
      const values = await form.validateFields(['dateTimeRange']);
      const [startDateTime, endDateTime] = values.dateTimeRange;

      // Validate that start is before end
      if (startDateTime.isAfter(endDateTime)) {
        message.error('Start date/time cannot be after end date/time');
        return;
      }

      setLoading(true);

      // Save successful search to recent searches
      saveToRecentSearches(startDateTime, endDateTime);

      // Call parent handler
      if (onDateRangeSubmit) {
        onDateRangeSubmit(startDateTime, endDateTime);
      }

      message.success('Date range submitted successfully');
    } catch (error) {
      console.error('Date range submission error:', error);
      message.error('Failed to submit date range');
    } finally {
      setLoading(false);
    }
  };

  // Handle batch selection changes
  const handleBatchSelectionChange = (selectedValues: string[]) => {
    if (selectedValues.length > compareLimit) {
      message.warning(`You can select maximum ${compareLimit} batches for comparison`);
      return;
    }

    if (onBatchSelectionChange) {
      onBatchSelectionChange(selectedValues);
    }
  };

  // Check if Batch Comparison panel is already added
  const isBatchComparisonAdded = activePanels.includes(PLCComponentType.PLCBatchComparisonPanel);

  return (
    <div className="view-sidebar pl-0 pt-4 pb-4 pr-4 h-full flex flex-col shadow-none">
      <div className="sidebar-header mb-4">
        <Space direction="vertical" style={{ width: '100%' }}>
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={handleBackClick}
            style={{ padding: 0 }}
          >
            Back
          </Button>

          <div style={{ textAlign: 'center', padding: '20px 0' }}>
            <Title level={4} className="text-gray-700 mb-2">PLC Level Exploration</Title>
            <Text className="text-gray-500 text-sm">Drag and drop panels to analyze PLC data</Text>
          </div>
        </Space>
      </div>

      {/* Date Range Selection Section */}
      <div className="mb-4">
        <Form form={form} layout="vertical">
          <div className="bg-gray-50 p-3 rounded-lg border">
            <Title level={5} className="mb-3 text-gray-700">
              📅 Date Range Selection
            </Title>

            <Form.Item
              name="dateTimeRange"
              label="Select Date & Time Range"
              rules={[
                { required: true, message: 'Please select date and time range!' }
              ]}
            >
              <RangePicker
                showTime={{ format: 'HH:mm:ss' }}
                format="YYYY-MM-DD HH:mm:ss"
                placeholder={['Start Date & Time', 'End Date & Time']}
                presets={createDatePresets()}
                style={{ width: '100%' }}
                size="small"
              />
            </Form.Item>

            <Button
              type="primary"
              onClick={handleDateRangeSubmit}
              loading={loading}
              block
              size="small"
            >
              Submit Date Range
            </Button>
          </div>
        </Form>
      </div>

      {/* Batch Selection Section */}
      {availableBatches.length > 0 && (
        <div className="mb-4">
          <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
            <Title level={5} className="mb-3 text-blue-700">
              🔬 Batch Selection ({selectedBatches.length}/{compareLimit})
            </Title>

            <Select
              mode="multiple"
              placeholder="Select batches to compare"
              value={selectedBatches}
              onChange={handleBatchSelectionChange}
              style={{ width: '100%' }}
              size="small"
              loading={isLoadingBatches}
              maxTagCount={2}
              maxTagTextLength={8}
            >
              {availableBatches.map(batchId => {
                const qualityInfo = qualityBatchIds.find(q => q.batch_id === batchId);
                const isGood = qualityInfo?.is_batch_good || false;
                const quality = qualityInfo?.batch_quality || 0;

                return (
                  <Option key={batchId} value={batchId}>
                    <div className="flex justify-between items-center">
                      <span>{batchId}</span>
                      <span
                        className={`text-xs px-1 rounded ${
                          isGood ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
                        }`}
                      >
                        {quality.toFixed(1)}
                      </span>
                    </div>
                  </Option>
                );
              })}
            </Select>

            {selectedBatches.length > 0 && (
              <div className="mt-2 text-xs text-gray-600">
                Selected: {selectedBatches.join(', ')}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Panels Section */}
      <div className="flex-1">
        <Collapse
          defaultActiveKey={['panels']}
          expandIconPosition="end"
          expandIcon={({ isActive }) => (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              {isActive ?
                <MinusOutlined style={{ fontSize: '16px' }} /> :
                <PlusOutlined style={{ fontSize: '16px' }} />
              }
            </div>
          )}
          className="plc-sidebar-collapse"
        >
          <Panel
            header={
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Title level={5} style={{ margin: 0 }}>Panels</Title>
              </div>
            }
            key="panels"
            style={{
              borderRadius: 0,
              backgroundColor: '#E9E9F5',
              marginBottom: '15px',
              border: '1px solid #b7b6b6'
            }}
          >
            <div className="plc-panels-list">
              <ul className="component-list">
                {/* Batch Comparison Panel */}
                <li
                  className={`component-item p-2 mb-2 rounded cursor-pointer ${
                    isBatchComparisonAdded
                      ? 'opacity-50 cursor-not-allowed'
                      : 'hover:bg-gray-100'
                  }`}
                  draggable={!isBatchComparisonAdded}
                  onDragStart={(e) => {
                    if (isBatchComparisonAdded) {
                      e.preventDefault();
                      return;
                    }
                    e.dataTransfer.setData('component', PLCComponentType.PLCBatchComparisonPanel);
                    e.dataTransfer.effectAllowed = 'move';
                  }}
                  onDragEnd={() => {
                    // Reset any drag styling if needed
                  }}
                >
                  Batch Comparison {isBatchComparisonAdded && <span className="added-badge ml-2 text-xs bg-gray-200 px-1 rounded">Added</span>}
                </li>
              </ul>
            </div>
          </Panel>
        </Collapse>
      </div>
    </div>
  );
};

export default PLCSidebar;
