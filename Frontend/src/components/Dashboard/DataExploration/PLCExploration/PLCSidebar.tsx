import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import {
  Button,
  Space,
  Collapse,
  Typography,
  DatePicker,
  Form,
  Select,
  message,
  Spin
} from 'antd';
import {
  ArrowLeftOutlined,
  PlusOutlined,
  MinusOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { PLCComponentType, PLCSidebarProps } from './types/PLCTypes';
import { postRequest } from '../../../../utils/apiHandler';
import dayjs from 'dayjs';

const { Panel } = Collapse;
const { Title } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

const PLCSidebar: React.FC<PLCSidebarProps> = ({
  onBackClick,
  activePanels = [],
  availableDataSources = [],
  onDataSourceSelect,
  onDateRangeSubmit,
  onBatchSelectionChange,
  availableBatches = [],
  qualityBatchIds = [],
  selectedBatches = [],
  compareLimit = 5,
  isLoadingBatches = false,
  persistedDateRange = null
}) => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [batchMetaLoading, setBatchMetaLoading] = useState(false);

  // Get selected systems from Redux state
  const selectSystems = useSelector((state: any) => state.systems.systems);

  // Recent searches management
  const [recentSearches, setRecentSearches] = useState<Array<{label: string, value: [any, any]}>>([]);
  const RECENT_SEARCHES_KEY = 'plc_recent_date_searches';
  const MAX_RECENT_SEARCHES = 5;

  // Helper function to extract system names from selected systems
  const getSystemNames = () => {
    if (!selectSystems || !selectSystems.length || !selectSystems[0]?.systems) {
      return [];
    }

    return selectSystems[0].systems.map((system: any) => ({
      systemId: system.systemId,
      systemName: system.systemName
    }));
  };

  // Function to fetch batch metadata (copied from drawer)
  const fetchBatchMetadata = async (startDateTime: any, endDateTime: any) => {
    setBatchMetaLoading(true);
    try {
      const systems = getSystemNames();
      const systemNames = systems.map((s: any) => s.systemName).join(' + ') || '';

      const apiPayload = {
        start_datetime: startDateTime.format('YYYY-MM-DD HH:mm'),
        end_datetime: endDateTime.format('YYYY-MM-DD HH:mm'),
        system: systemNames
      };

      const response = await postRequest('/file/explore-plc/get-batch-meta', apiPayload);

      if (response.data.data) {
        const { batch_ids, quality_batch_ids, compare_limit } = response.data.data;

        // Call parent handlers to update state
        if (onDateRangeSubmit) {
          onDateRangeSubmit(startDateTime, endDateTime);
        }

        return true;
      } else {
        message.error('Failed to load batch metadata');
        return false;
      }
    } catch (error) {
      console.error('Error fetching batch metadata:', error);
      message.error('Failed to fetch batch metadata');
      return false;
    } finally {
      setBatchMetaLoading(false);
    }
  };

  // Render enhanced batch option with quality information (copied from drawer)
  const renderBatchOption = (batchInfo: { batch_id: string; batch_quality: number; is_batch_good: boolean }) => {
    const { batch_id, batch_quality, is_batch_good } = batchInfo;
    const qualityColor = is_batch_good ? '#52c41a' : '#ff4d4f';
    const qualityBg = is_batch_good ? '#f6ffed' : '#fff2f0';

    return (
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: '4px 8px',
        backgroundColor: qualityBg,
        borderRadius: '4px',
        border: `1px solid ${qualityColor}20`
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '6px'
        }}>
          <span style={{
            fontWeight: 500,
            color: '#333'
          }}>
            {batch_id}
          </span>
        </div>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '4px'
        }}>
          <span style={{
            fontSize: '11px',
            color: qualityColor,
            fontWeight: 600,
            backgroundColor: qualityColor + '15',
            padding: '2px 6px',
            borderRadius: '10px'
          }}>
            {batch_quality.toFixed(2)}
          </span>
          <span style={{
            fontSize: '14px'
          }}>
            {is_batch_good ? '✅' : '❌'}
          </span>
        </div>
      </div>
    );
  };

  // Load recent searches from localStorage
  useEffect(() => {
    const saved = localStorage.getItem(RECENT_SEARCHES_KEY);
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        const validSearches = parsed.map((search: any) => ({
          label: search.label,
          value: [dayjs(search.value[0]), dayjs(search.value[1])] as [any, any]
        }));
        setRecentSearches(validSearches);
      } catch (error) {
        console.error('Error loading recent searches:', error);
      }
    }

    // Set default date range (last 7 days)
    const defaultStartDateTime = dayjs().subtract(7, 'days').startOf('day');
    const defaultEndDateTime = dayjs().endOf('day');

    form.setFieldsValue({
      dateTimeRange: [defaultStartDateTime, defaultEndDateTime],
    });
  }, [form]);

  // Initialize form with default values - copied from drawer
  useEffect(() => {
    // Use persisted date range if available, otherwise default to last 7 days
    let startDateTime, endDateTime;

    if (persistedDateRange) {
      startDateTime = dayjs(persistedDateRange.startDateTime);
      endDateTime = dayjs(persistedDateRange.endDateTime);
    } else {
      startDateTime = dayjs().subtract(7, 'days').startOf('day');
      endDateTime = dayjs().endOf('day');
    }

    form.setFieldsValue({
      dateTimeRange: [startDateTime, endDateTime],
      selectedBatches: selectedBatches,
    });
  }, [form, selectedBatches, persistedDateRange]);

  // Save a new date range to recent searches
  const saveToRecentSearches = (startDateTime: any, endDateTime: any) => {
    const newSearch = {
      label: `${startDateTime.format('MMM DD')} - ${endDateTime.format('MMM DD, YYYY')}`,
      value: [startDateTime, endDateTime] as [any, any]
    };

    const existing = recentSearches.filter(search =>
      search.label !== newSearch.label
    );

    const updated = [newSearch, ...existing].slice(0, MAX_RECENT_SEARCHES);
    setRecentSearches(updated);

    const toSave = updated.map(search => ({
      label: search.label,
      value: [search.value[0].toISOString(), search.value[1].toISOString()]
    }));

    localStorage.setItem(RECENT_SEARCHES_KEY, JSON.stringify(toSave));
  };

  // Create presets for RangePicker (common ranges + recent searches) - copied from drawer
  const createDatePresets = () => {
    const commonPresets = [
      {
        label: 'Last 24 hours',
        value: () => [dayjs().subtract(1, 'day'), dayjs()] as [any, any]
      },
      {
        label: 'Last 7 days',
        value: () => [dayjs().subtract(7, 'days'), dayjs()] as [any, any]
      },
      {
        label: 'Last 30 days',
        value: () => [dayjs().subtract(30, 'days'), dayjs()] as [any, any]
      },
      {
        label: 'This month',
        value: () => [dayjs().startOf('month'), dayjs()] as [any, any]
      }
    ];

    // Add separator if there are recent searches
    const allPresets = [...commonPresets];

    if (recentSearches.length > 0) {
      // Add recent searches without separator (separator causes issues)
      const recentPresets = recentSearches.map(search => ({
        label: `📅 ${search.label}`,
        value: () => search.value as [any, any]
      }));

      allPresets.push(...recentPresets);
    }

    return allPresets;
  };

  const handleBackClick = () => {
    if (onBackClick) {
      onBackClick();
      navigate('/?tab=data');
    } else {
      // Default behavior: navigate back to data tab
      navigate('/?tab=data');
    }
  };

  // Handle form submission (date range submission) - copied from drawer
  const handleFormSubmit = async (values: any) => {
    // Validate that date time range is provided
    if (!values.dateTimeRange || !values.dateTimeRange[0] || !values.dateTimeRange[1]) {
      message.error('Please select date and time range before submitting');
      return;
    }

    const [startDateTime, endDateTime] = values.dateTimeRange;

    // Validate that start is before end
    if (startDateTime.isAfter(endDateTime)) {
      message.error('Start date/time cannot be after end date/time');
      return;
    }

    setLoading(true);
    try {
      // Step 1: Fetch batch metadata first
      const success = await fetchBatchMetadata(startDateTime, endDateTime);

      if (!success) {
        // Keep form open on error, don't proceed
        setLoading(false);
        return;
      }

      // Save successful search to recent searches
      saveToRecentSearches(startDateTime, endDateTime);

      message.success('Date range submitted and batches loaded successfully');
    } catch (error) {
      console.error('Error in form submission:', error);
      message.error('Failed to submit date range');
    } finally {
      setLoading(false);
    }
  };

  // Handle batch selection changes - updated to match drawer behavior
  const handleBatchSelectionChange = (selectedValues: string[]) => {
    if (selectedValues.length > compareLimit) {
      message.warning(`Maximum ${compareLimit} batches allowed`);
      return;
    }

    if (onBatchSelectionChange) {
      onBatchSelectionChange(selectedValues);
    }
  };

  // Check if Batch Comparison panel is already added
  const isBatchComparisonAdded = activePanels.includes(PLCComponentType.PLCBatchComparisonPanel);

  return (
    <div className="view-sidebar pl-0 pt-4 pb-4 pr-4 h-full flex flex-col shadow-none">
      <div className="sidebar-header mb-4">
        <Space direction="vertical" style={{ width: '100%' }}>
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={handleBackClick}
            style={{ padding: 0 }}
          >
            Back
          </Button>

          <div style={{ textAlign: 'center', padding: '20px 0' }}>
            <Title level={4} className="text-gray-700 mb-2">PLC Level Exploration</Title>
          </div>
        </Space>
      </div>

      {/* Date Range Selection Section - Exact copy from drawer */}
      <div className="mb-4">
        <Form form={form} layout="vertical" onFinish={handleFormSubmit}>
          <div style={{
            background: '#fafafa',
            borderRadius: '12px',
            padding: '20px',
            border: '1px solid #e8e8e8',
            boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
          }}>
            {/* Date and Time Range Selection */}
            <Form.Item
              label={<span style={{ fontSize: '13px', fontWeight: 500, color: '#666' }}>Date & Time Range</span>}
              name="dateTimeRange"
              style={{ marginBottom: '16px' }}
              rules={[
                { required: true, message: 'Please select date and time range!' }
              ]}
            >
              <RangePicker
                showTime={{ format: 'HH:mm:ss' }}
                format="YYYY-MM-DD HH:mm:ss"
                placeholder={['Start Date & Time', 'End Date & Time']}
                disabled={batchMetaLoading}
                style={{ width: '100%', borderRadius: '8px' }}
                size="middle"
                presets={createDatePresets()}
              />
            </Form.Item>

            {/* Submit Button in Data Selection Zone */}
            <div style={{ marginTop: '20px' }}>
              <Button
                type="primary"
                htmlType="submit"
                loading={batchMetaLoading}
                disabled={batchMetaLoading}
                style={{
                  width: '100%',
                  height: '40px',
                  fontSize: '14px',
                  fontWeight: 600,
                  borderRadius: '8px',
                  background: '#1890ff',
                  border: 'none'
                }}
                size="large"
              >
                {batchMetaLoading ? 'Fetching Batches...' : 'Submit Date Range'}
              </Button>
            </div>
          </div>
        </Form>
      </div>

      {/* Batch Selection Section - Exact copy from drawer */}
      {availableBatches.length > 0 && (
        <div className="mb-4">
          <div style={{
            background: '#f0f8ff',
            borderRadius: '8px',
            padding: '16px',
            border: '1px solid #91d5ff',
            marginTop: '16px'
          }}>
            <div style={{
              marginBottom: '12px',
              fontSize: '13px',
              fontWeight: 500,
              color: '#1890ff'
            }}>
              📊 Batch Selection (Max: {compareLimit})
            </div>

            <Form.Item
              label={<span style={{ fontSize: '13px', fontWeight: 500, color: '#666' }}>Select Batches</span>}
              name="selectedBatches"
              style={{ marginBottom: '12px' }}
              rules={[
                { required: true, message: 'Please select at least one batch!' },
                {
                  validator: (_, value) => {
                    if (value && value.length > compareLimit) {
                      return Promise.reject(new Error(`Maximum ${compareLimit} batches allowed`));
                    }
                    return Promise.resolve();
                  }
                }
              ]}
            >
              <Select
                mode="multiple"
                placeholder={`Select up to ${compareLimit} batches`}
                value={selectedBatches}
                onChange={handleBatchSelectionChange}
                disabled={loading || batchMetaLoading}
                size="middle"
                style={{
                  borderRadius: '8px',
                  width: '100%'
                }}
                maxTagCount="responsive"
                optionLabelProp="label"
                showSearch
                filterOption={(input, option) =>
                  (option?.label as string)?.toLowerCase()?.includes(input.toLowerCase())
                }
              >
                {qualityBatchIds.length > 0 ? (
                  qualityBatchIds.map(batchInfo => (
                    <Option
                      key={batchInfo.batch_id}
                      value={batchInfo.batch_id}
                      label={batchInfo.batch_id}
                    >
                      {renderBatchOption(batchInfo)}
                    </Option>
                  ))
                ) : (
                  availableBatches.map(batch => (
                    <Option key={batch} value={batch} label={batch}>
                      {batch}
                    </Option>
                  ))
                )}
              </Select>
            </Form.Item>
          </div>
        </div>
      )}

      {/* Panels Section */}
      <div className="flex-1">
        <Collapse
          defaultActiveKey={['panels']}
          expandIconPosition="end"
          expandIcon={({ isActive }) => (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              {isActive ?
                <MinusOutlined style={{ fontSize: '16px' }} /> :
                <PlusOutlined style={{ fontSize: '16px' }} />
              }
            </div>
          )}
          className="plc-sidebar-collapse"
        >
          <Panel
            header={
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Title level={5} style={{ margin: 0 }}>Panels</Title>
              </div>
            }
            key="panels"
            style={{
              borderRadius: 0,
              backgroundColor: '#E9E9F5',
              marginBottom: '15px',
              border: '1px solid #b7b6b6'
            }}
          >
            <div className="plc-panels-list">
              <ul className="component-list">
                {/* Batch Comparison Panel */}
                <li
                  className={`component-item p-2 mb-2 rounded cursor-pointer ${
                    isBatchComparisonAdded
                      ? 'opacity-50 cursor-not-allowed'
                      : 'hover:bg-gray-100'
                  }`}
                  draggable={!isBatchComparisonAdded}
                  onDragStart={(e) => {
                    if (isBatchComparisonAdded) {
                      e.preventDefault();
                      return;
                    }
                    e.dataTransfer.setData('component', PLCComponentType.PLCBatchComparisonPanel);
                    e.dataTransfer.effectAllowed = 'move';
                  }}
                  onDragEnd={() => {
                    // Reset any drag styling if needed
                  }}
                >
                  Batch Comparison {isBatchComparisonAdded && <span className="added-badge ml-2 text-xs bg-gray-200 px-1 rounded">Added</span>}
                </li>
              </ul>
            </div>
          </Panel>
        </Collapse>
      </div>
    </div>
  );
};

export default PLCSidebar;
